version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: customer-postgres
    environment:
      POSTGRES_DB: customer_service
      POSTGRES_USER: nestjs
      POSTGRES_PASSWORD: password
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - customer-network

  # Customer Backend API
  customer-backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: customer-backend
    ports:
      - "3060:3060"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=******************************************/customer_service?schema=public
      - PORT=3060
      - JWT_SECRET=customer-service-jwt-secret-key-for-development
      - ALLOWED_ORIGIN_1=http://localhost:3000
      - ALLOWED_ORIGIN_2=http://localhost:3002
      - SOURCE_IP=0.0.0.0
      - ALLOWED_ORIGIN_3=http://customer-embed:3002
      - LOC<PERSON>_BACKEND_DOMAIN=http://ng-customer-local.dev1.ngnair.com:3060
      - LOCAL_FRONTEND_DOMAIN=http://ng-customer-fe-local.dev1.ngnair.com:3061
      - LOCAL_ADMIN_DOMAIN=http://ng-customer-admin-local.dev1.ngnair.com:3062
      - AUTH_FRONTEND_URL=https://ng-auth-fe-dev.dev1.ngnair.com
      - AUTH_JWKS_URL=https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks
      - ACCESS_TOKEN_ENCRYPTION_KEY=b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8
      - COOKIE_SECRET=your-secret-key-here
      - CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002,http://ng-customer-admin-local.dev1.ngnair.com:3062,http://ng-customer-fe-local.dev1.ngnair.com:3061
    depends_on:
      - postgres
    networks:
      - customer-network
    volumes:
      - ./backend:/app
    command: sh -c "npx prisma migrate deploy && npm start"

  # Customer Admin Frontend
  customer-admin:
    build:
      context: ./customer-admin
      dockerfile: Dockerfile
    container_name: customer-admin
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:3001
      - NEXT_PUBLIC_GRAPHQL_URL=http://localhost:3001/graphql
    depends_on:
      - customer-backend
    networks:
      - customer-network

  # Customer Embed Frontend
  customer-embed:
    build:
      context: ./customer-embed
      dockerfile: Dockerfile
    container_name: customer-embed
    ports:
      - "3002:3002"
    environment:
      - NEXT_PUBLIC_API_BASE_URL=http://localhost:3001/api/v1
    depends_on:
      - customer-backend
    networks:
      - customer-network

  # Redis (Optional - for caching and sessions)
  redis:
    image: redis:7-alpine
    container_name: customer-redis
    ports:
      - "6379:6379"
    networks:
      - customer-network

volumes:
  postgres_data:

networks:
  customer-network:
    driver: bridge
