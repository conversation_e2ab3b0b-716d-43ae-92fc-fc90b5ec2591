// import { FormInput } from '@/components/globals';
// import { message, emailPatternRegex } from '@/components/shared/utils';
// import {
//   Gateway_UpdateCustomerDocument,
//   GatewayUniCustomerOutput,
// } from '@/graphql/generated/graphql';
// import { useMutation } from '@apollo/client';
// import { Card, Button } from 'flowbite-react';
// import { Dispatch, SetStateAction, useEffect } from 'react';
// import { FormProvider, useForm } from 'react-hook-form';
// import { toast } from 'react-toastify';
// import { UpdateFormData } from '../utils';
// import { useLocationSelector } from '@/components/hooks';
// import FormPhoneNumber from '@/components/globals/form-phone-number/form-phonenumber.index';
// import { apolloClient } from '@/lib/graphql/ApolloClient';
// import StateCountryForms from '@/components/shared/components/stateCountryFormSelect';
import { GatewayUniCustomerOutput } from '@/graphql/generated/graphql';
import { Dispatch, SetStateAction } from 'react';

type CustomerUpdateProps = {
  onClose: () => void;
  customer: GatewayUniCustomerOutput;
  setParentLoading: Dispatch<SetStateAction<boolean>>;
  deleteAction: JSX.Element;
};

// export const CustomerUpdate = ({
//   onClose,
//   customer,
//   deleteAction,
//   setParentLoading,
// }: CustomerUpdateProps) => {
//   const customerFormMethods = useForm<UpdateFormData>({});
//   const { locationSelectorElement, locationFilter } = useLocationSelector({
//     readonly: true,
//     onlyActive: true,
//   });

//   const { setValue } = customerFormMethods;
//   const [updateCustomerMutation, { loading: updateCustomerLoading }] = useMutation(
//     Gateway_UpdateCustomerDocument,
//     {
//       onCompleted: (_) => {
//         toast.success(message.api.successUpdate('Customer'));
//         onClose();
//       },
//       onError: (error) => {
//         toast.error(message.api.errorUpdate('Customer', error.message));
//       },
//     },
//   );

//   useEffect(() => {
//     if (customer) {
//       setValue('nameOnCard', customer?.nameOnCard ?? '');
//       setValue('email', customer?.email ?? '');
//       setValue('phone', customer?.phone ?? '');
//       setValue('billingAddress', customer?.billingAddress ?? '');
//       setValue('city', customer?.billingCity ?? '');
//       setValue('state', customer?.billingState ?? '');
//       setValue('zipCode', customer?.billingZip ?? '');
//       setValue('country', customer?.billingCountry ?? '');
//     }
//   }, [customer]);

//   const onSubmitForm = async (data: UpdateFormData) => {
//     try {
//       await updateCustomerMutation({
//         variables: {
//           input: {
//             groupID: locationFilter?.id ?? '',
//             data: {
//               customerID: customer.id ?? '',
//               form: {
//                 nameOnCard: data.nameOnCard,
//                 email: data.email,
//                 phone: data.phone,
//                 billingAddress: data.billingAddress,
//                 billingCity: data.city,
//                 billingState: data.state,
//                 billingZip: data.zipCode,
//                 billingCountry: data.country,
//               },
//             },
//           },
//         },
//       });

//       await apolloClient.resetStore();
//     } catch (e) {
//       console.error('Update Customer Mutation error: ', e);
//     }
//   };

//   useEffect(() => {
//     setParentLoading(updateCustomerLoading);
//   }, [updateCustomerLoading]);

//   return (
//     <div>
//       <FormProvider {...customerFormMethods}>
//         <form onSubmit={customerFormMethods.handleSubmit(onSubmitForm)} className="space-y-4">
//           <div className="w-full md:w-1/4">{locationSelectorElement}</div>
//           <div className="grid grid-cols-1 gap-3 md:grid-cols-5">
//             <div className="col-span-5 space-y-4">
//               <Card>
//                 <div className="grid grid-cols-1 gap-3 md:grid-cols-2">
//                   <div>
//                     <FormInput
//                       id="nameOnCard"
//                       name="nameOnCard"
//                       label="Name on Card"
//                       rules={{ required: message.requiredField }}
//                     />
//                     <FormInput
//                       id="email"
//                       name="email"
//                       label="Email"
//                       type="email"
//                       rules={{
//                         required: message.requiredField,
//                         pattern: {
//                           value: emailPatternRegex,
//                           message: message.emailPattern,
//                         },
//                       }}
//                     />
//                     {/* <FormPhoneNumber
//                       id="phone"
//                       name="phone"
//                       label="Phone Number"
//                       // mask="(*************"
//                       rules={{ required: message.requiredField }}
//                     /> */}
//                     <FormPhoneNumber
//                       id="phone"
//                       name="phone"
//                       label="Phone Number"
//                       country="us"
//                       preferredCountries={['us', 'ca']}
//                       rules={{ required: message.requiredField }}
//                     />
//                   </div>
//                   <div>
//                     <FormInput
//                       id="billingAddress"
//                       name="billingAddress"
//                       label="Billing Address"
//                       rules={{ required: message.requiredField }}
//                     />
//                     <StateCountryForms
//                       countryKey="country"
//                       stateKey="state"
//                       message={message}
//                       methods={customerFormMethods}
//                     />
//                     <FormInput
//                       id="city"
//                       name="city"
//                       label="City"
//                       rules={{ required: message.requiredField }}
//                     />

//                     <FormInput
//                       id="zipCode"
//                       name="zipCode"
//                       label="Zip Code"
//                       type="number"
//                       tooltip={message.tooltip.zipCode}
//                       rules={{
//                         required: message.requiredField,
//                         maxLength: {
//                           value: 5,
//                           message: message.maxLength(5),
//                         },
//                       }}
//                     />
//                     {/* HIDE atm Nov. 3 */}
//                     {/* <FormSelect
//                               id="country"
//                               name="country"
//                               label="Country"
//                               rules={{ required: message.requiredField }}
//                               options={COUNTRIES}
//                             /> */}
//                   </div>
//                 </div>
//               </Card>
//               <div className="flex justify-between">
//                 <Button type="submit" color="blue">
//                   Update Customer
//                 </Button>
//                 {deleteAction}
//               </div>
//             </div>
//           </div>
//         </form>
//       </FormProvider>
//     </div>
//   );
// Temporarily disabled customer functionality
export const CustomerUpdate = () => {
  return null;
};
