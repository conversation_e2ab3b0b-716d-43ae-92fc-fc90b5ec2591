import React, { ReactNode } from 'react';
import { useAuth } from '../hooks/useAuth';

interface ProtectedRouteProps {
  children: ReactNode;
  requiredRole?: string;
  requiredPermissions?: string[];
  fallback?: ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  requiredPermissions,
  fallback = <div>Loading...</div>
}) => {
  const { user, isAuthenticated, isLoading, login } = useAuth();

  // Show loading state
  if (isLoading) {
    return <>{fallback}</>;
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated || !user) {
    // Check if debug mode is enabled
    const isDebugMode = process.env.NEXT_PUBLIC_AUTH_DEBUG_MODE === 'true';

    if (isDebugMode) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Authentication Debug</h2>
              <p className="text-gray-600">Debug mode is enabled</p>
            </div>

            <div className="space-y-4">
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <h3 className="text-sm font-medium text-red-800 mb-2">Authentication Status</h3>
                <p className="text-sm text-red-700">Not authenticated</p>
                {error && (
                  <p className="text-sm text-red-700 mt-1">Error: {error}</p>
                )}
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <h3 className="text-sm font-medium text-blue-800 mb-2">Cookie Information</h3>
                <p className="text-sm text-blue-700">
                  Access Token: {typeof window !== 'undefined' && document.cookie.includes('access_token') ? '✅ Present' : '❌ Missing'}
                </p>
                <p className="text-sm text-blue-700">
                  Refresh Token: {typeof window !== 'undefined' && document.cookie.includes('refresh_token') ? '✅ Present' : '❌ Missing'}
                </p>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                <h3 className="text-sm font-medium text-yellow-800 mb-2">Debug Actions</h3>
                <div className="space-y-2">
                  <button
                    onClick={() => {
                      if (typeof window !== 'undefined') {
                        console.log('Cookies:', document.cookie);
                        console.log('Auth Error:', (window as any).authError);
                      }
                    }}
                    className="w-full text-left text-sm text-yellow-700 hover:text-yellow-900 underline"
                  >
                    📋 Log cookies to console
                  </button>
                  <button
                    onClick={() => login()}
                    className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
                  >
                    🔐 Go to Login
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    // Normal mode - redirect to login
    React.useEffect(() => {
      login();
    }, [login]);

    return <div>Redirecting to login...</div>;
  }

  // Check role requirements
  if (requiredRole && user.role !== requiredRole) {
    return <div>Access denied: Insufficient role permissions</div>;
  }

  // Check permission requirements
  if (requiredPermissions && requiredPermissions.length > 0) {
    const hasPermissions = requiredPermissions.every(permission => 
      user.permissions?.includes(permission)
    );
    
    if (!hasPermissions) {
      return <div>Access denied: Insufficient permissions</div>;
    }
  }

  return <>{children}</>;
};
