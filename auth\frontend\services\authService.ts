import { AuthConfig, User, JWTPayload } from '../types/auth.types';

export class AuthService {
  private config: AuthConfig;

  constructor(config: AuthConfig) {
    this.config = config;
  }

  /**
   * Check if user is authenticated by checking for valid cookies
   */
  isAuthenticated(): boolean {
    if (typeof window === 'undefined') return false;
    
    const accessToken = this.getCookie(this.config.cookieNames.accessToken);
    const refreshToken = this.getCookie(this.config.cookieNames.refreshToken);
    
    return !!(accessToken && refreshToken);
  }

  /**
   * Get cookie value by name
   */
  private getCookie(name: string): string | null {
    if (typeof window === 'undefined') return null;
    
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
      return parts.pop()?.split(';').shift() || null;
    }
    return null;
  }

  /**
   * Redirect to auth service for login
   */
  login(redirectUrl?: string): void {
    if (typeof window === 'undefined') return;
    
    const currentUrl = redirectUrl || window.location.href;
    const encodedRedirectUrl = encodeURIComponent(currentUrl);
    const authUrl = `${this.config.authFrontendUrl}/login?redirect=${encodedRedirectUrl}`;
    
    window.location.href = authUrl;
  }

  /**
   * Logout by clearing cookies and redirecting to auth service
   */
  logout(): void {
    if (typeof window === 'undefined') return;
    
    // Clear cookies
    this.clearCookie(this.config.cookieNames.accessToken);
    this.clearCookie(this.config.cookieNames.refreshToken);
    
    // Redirect to auth service logout
    const currentUrl = window.location.origin;
    const encodedRedirectUrl = encodeURIComponent(currentUrl);
    const logoutUrl = `${this.config.authFrontendUrl}/logout?redirect=${encodedRedirectUrl}`;
    
    window.location.href = logoutUrl;
  }

  /**
   * Clear a cookie
   */
  private clearCookie(name: string): void {
    if (typeof window === 'undefined') return;
    
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
  }

  /**
   * Get user info from backend (backend will decrypt and verify tokens)
   */
  async getCurrentUser(): Promise<User | null> {
    try {
      // Always use production backend for API calls
      const apiUrl = process.env.NEXT_PUBLIC_CUSTOMER_API_URL || 'https://ng-customer-dev.dev1.ngnair.com';
      const response = await fetch(`${apiUrl}/auth/me/cookies`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          // Token expired or invalid, redirect to login
          this.login();
          return null;
        }
        throw new Error('Failed to get user info');
      }

      const user = await response.json();
      return user;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  /**
   * Refresh authentication by calling backend
   */
  async refreshAuth(): Promise<boolean> {
    try {
      // Always use production backend for API calls
      const apiUrl = process.env.NEXT_PUBLIC_CUSTOMER_API_URL || 'https://ng-customer-dev.dev1.ngnair.com';
      const response = await fetch(`${apiUrl}/auth/refresh`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          // Refresh token expired, redirect to login
          this.login();
          return false;
        }
        throw new Error('Failed to refresh auth');
      }

      return true;
    } catch (error) {
      console.error('Error refreshing auth:', error);
      return false;
    }
  }
}

// Default configuration - these should be set in your .env file
export const defaultAuthConfig: AuthConfig = {
  authFrontendUrl: process.env.NEXT_PUBLIC_AUTH_FRONTEND_URL || 'https://ng-auth-fe-dev.dev1.ngnair.com',
  authJwksUrl: process.env.NEXT_PUBLIC_AUTH_JWKS_URL || 'https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks',
  encryptionKey: process.env.ACCESS_TOKEN_ENCRYPTION_KEY || 'b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8',
  cookieNames: {
    accessToken: 'access_token',
    refreshToken: 'refresh_token',
  },
};

// Export singleton instance
export const authService = new AuthService(defaultAuthConfig);
