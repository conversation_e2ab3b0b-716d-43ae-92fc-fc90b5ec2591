import { Injectable, CanActivate, ExecutionContext, UnauthorizedException, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthSharedService } from './auth.service';

// Decorators for setting metadata
export const Roles = (roles: string[]) => {
  return (target: any, propertyKey?: string | symbol, descriptor?: PropertyDescriptor) => {
    Reflect.defineMetadata('roles', roles, target, propertyKey || '');
  };
};

export const Permissions = (permissions: string[]) => {
  return (target: any, propertyKey?: string | symbol, descriptor?: PropertyDescriptor) => {
    Reflect.defineMetadata('permissions', permissions, target, propertyKey || '');
  };
};

export const Public = () => {
  return (target: any, propertyKey?: string | symbol, descriptor?: PropertyDescriptor) => {
    Reflect.defineMetadata('isPublic', true, target, propertyKey || '');
  };
};

@Injectable()
export class AuthSharedGuard implements CanActivate {
  constructor(
    private authService: AuthSharedService,
    private reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // Check if route is public
    const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
      context.getHandler(),
      context.getClass(),
    ]);

    if (isPublic) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    
    try {
      // Extract cookies from request
      const cookies = request.cookies || {};
      
      // Authenticate user
      const user = await this.authService.authenticateFromCookies(cookies);
      
      // Attach user to request
      request.user = user;

      // Check role requirements
      const requiredRoles = this.reflector.getAllAndOverride<string[]>('roles', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (requiredRoles && requiredRoles.length > 0) {
        const hasRole = requiredRoles.some(role => this.authService.hasRole(user, role));
        if (!hasRole) {
          throw new ForbiddenException(`Required role: ${requiredRoles.join(' or ')}`);
        }
      }

      // Check permission requirements
      const requiredPermissions = this.reflector.getAllAndOverride<string[]>('permissions', [
        context.getHandler(),
        context.getClass(),
      ]);

      if (requiredPermissions && requiredPermissions.length > 0) {
        const hasPermissions = this.authService.hasPermissions(user, requiredPermissions);
        if (!hasPermissions) {
          throw new ForbiddenException(`Required permissions: ${requiredPermissions.join(', ')}`);
        }
      }

      return true;
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      throw new UnauthorizedException('Authentication required');
    }
  }
}

// Decorator to get current user in controllers
export const CurrentUser = () => {
  return (target: any, propertyKey: string | symbol | undefined, parameterIndex: number) => {
    return (target: any, propertyKey?: string, descriptor?: PropertyDescriptor) => {
      const existingMetadata = Reflect.getMetadata('custom:paramtypes', target, propertyKey || '') || [];
      existingMetadata[parameterIndex] = 'user';
      Reflect.defineMetadata('custom:paramtypes', existingMetadata, target, propertyKey || '');
    };
  };
};
