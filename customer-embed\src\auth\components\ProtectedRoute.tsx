import React, { ReactNode } from 'react';
import { useAuth } from '../hooks/useAuth';

interface ProtectedRouteProps {
  children: ReactNode;
  requiredRole?: string;
  requiredPermissions?: string[];
  fallback?: ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  requiredPermissions,
  fallback = <div>Loading...</div>
}) => {
  const { user, isAuthenticated, isLoading, login } = useAuth();

  // Show loading state
  if (isLoading) {
    return <>{fallback}</>;
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated || !user) {
    // Trigger login redirect
    React.useEffect(() => {
      login();
    }, [login]);
    
    return <div>Redirecting to login...</div>;
  }

  // Check role requirements
  if (requiredRole && user.role !== requiredRole) {
    return <div>Access denied: Insufficient role permissions</div>;
  }

  // Check permission requirements
  if (requiredPermissions && requiredPermissions.length > 0) {
    const hasPermissions = requiredPermissions.every(permission => 
      user.permissions?.includes(permission)
    );
    
    if (!hasPermissions) {
      return <div>Access denied: Insufficient permissions</div>;
    }
  }

  return <>{children}</>;
};
