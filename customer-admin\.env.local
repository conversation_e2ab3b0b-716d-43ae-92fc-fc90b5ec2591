# Customer Admin Dashboard Environment Variables

# Auth Configuration
NEXT_PUBLIC_AUTH_FRONTEND_URL=https://ng-auth-fe-dev.dev1.ngnair.com
NEXT_PUBLIC_AUTH_JWKS_URL=https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks
ACCESS_TOKEN_ENCRYPTION_KEY=b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8

# Local Domain Configuration
NEXT_PUBLIC_LOCAL_BACKEND_DOMAIN=http://ng-customer-local.dev1.ngnair.com:3060
NEXT_PUBLIC_LOCAL_FRONTEND_DOMAIN=http://ng-customer-fe-local.dev1.ngnair.com:3061
NEXT_PUBLIC_LOCAL_ADMIN_DOMAIN=http://ng-customer-admin-local.dev1.ngnair.com:3062

# Debug Mode (prevents auto-redirect for debugging)
NEXT_PUBLIC_AUTH_DEBUG_MODE=true

# API Endpoints
NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL=http://ng-customer-local.dev1.ngnair.com:3060/graphql
NEXT_PUBLIC_CUSTOMER_API_URL=http://ng-customer-local.dev1.ngnair.com:3060
OPEN_BANK_V2_API=http://ng-customer-local.dev1.ngnair.com:3060/api/v1

# Application Settings
NEXT_PUBLIC_APP_NAME=Customer Management System
NEXT_PUBLIC_SITE_URL=https://admin.example.com
NEXT_PUBLIC_ADMIN_EMAIL=<EMAIL>

# Image domains for Next.js
NEXT_PUBLIC_IMAGE_DOMAINS=localhost

# Application Port
PORT=3000
