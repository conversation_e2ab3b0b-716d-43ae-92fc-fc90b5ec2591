import { useRouter } from 'next/navigation';

type ChargeDetail = {
  description: string;
  amount: number;
};

type ChargeDetailSectionProps = {
  customer: {
    customerID: string | null | undefined;
  };
  charges: ChargeDetail[];
};

export const ChargeDetailSection = ({ customer, charges }: ChargeDetailSectionProps) => {
  const router = useRouter();
  const handleEditCustomer = () => {
    router.push(`/dashboard/customers?id=${customer?.customerID}`);
  };

  const totalAmount = charges.reduce((total, charge) => total + charge.amount, 0);

  return (
    <div className="mb-6 rounded-lg bg-gray-50 p-4">
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-lg font-semibold">Charge Cost Breakdown</h3>
        {/* <Button color="gray" size="xs" onClick={handleEditCustomer}>
          <HiOutlineExternalLink className="mr-2 h-4 w-4" />
          Edit
        </Button> */}
      </div>
      <div className="space-y-2">
        {charges.map((charge, index) => (
          <ChargeDetailItem key={index} description={charge.description} amount={charge.amount} />
        ))}
        <div className="border-t border-gray-200 pt-2 text-right">
          <span className="font-medium">Total: </span>
          <span className="text-gray-500">${totalAmount.toFixed(2)}</span>
        </div>
      </div>
    </div>
  );
};

const ChargeDetailItem = ({ description, amount }) => (
  <div className="flex justify-between py-1 text-sm">
    <span>{description}</span>
    <span className={`text-sm ${amount < 0 ? 'text-red-500' : 'text-gray-500'}`}>
      ${amount.toFixed(2)}
    </span>
  </div>
);

// Temporarily disabled customer functionality
export const ChargeSection = () => {
  return null;
};
