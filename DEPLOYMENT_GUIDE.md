# Deployment Guide - Customer Microservice

## Quick Start with Docker

### 1. Prerequisites
- Docker and Docker Compose installed
- Ports 3000, 3001, 3002, 5432, 6379 available

### 2. Deploy All Services
```bash
# Clone and navigate to project
cd customer

# Start all services
docker-compose up -d

# Check status
docker-compose ps
```

### 3. Access Applications
- **Customer Backend API**: http://localhost:3001
- **Customer Admin**: http://localhost:3000
- **Customer Embed**: http://localhost:3002
- **API Documentation**: http://localhost:3001/api
- **GraphQL Playground**: http://localhost:3001/graphql

## Manual Development Setup

### Backend (Port 3001)
```bash
cd backend

# Install dependencies
npm install

# Set up environment
cp .env.example .env
# Edit .env with your database settings

# Run database migrations
npx prisma migrate dev

# Build and start
npm run build
npm start
```

### Customer Admin (Port 3000)
```bash
cd customer-admin

# Install dependencies
npm install

# Start development server
npm run dev
```

### Customer Embed (Port 3002)
```bash
cd customer-embed

# Install dependencies
npm install

# Start development server
npm run dev
```

## Testing the Setup

### 1. API Testing
```bash
# Install axios for testing
npm install axios

# Run API tests
node test-api.js
```

### 2. Frontend Testing
- Visit http://localhost:3000 (should redirect to dashboard without login)
- Visit http://localhost:3002/demo (embedded form demo)

### 3. Integration Testing
```bash
# Test customer creation via API
curl -X POST http://localhost:3001/customers \
  -H "Content-Type: application/json" \
  -d '{
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "status": "ACTIVE",
    "type": "INDIVIDUAL"
  }'
```

## Production Deployment

### Environment Variables

#### Backend (.env)
```bash
NODE_ENV=production
PORT=3001
DATABASE_URL=********************************/customer_service
ALLOWED_ORIGIN_1=https://your-finance-app.com
ALLOWED_ORIGIN_2=https://your-embed-domain.com
REDIS_HOST=redis-host
REDIS_PORT=6379
```

#### Customer Embed (.env.local)
```bash
NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com/api/v1
```

### Docker Production Build
```bash
# Build production images
docker-compose -f docker-compose.prod.yml build

# Deploy to production
docker-compose -f docker-compose.prod.yml up -d
```

### Kubernetes Deployment
```yaml
# k8s/customer-backend.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: customer-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: customer-backend
  template:
    metadata:
      labels:
        app: customer-backend
    spec:
      containers:
      - name: customer-backend
        image: your-registry/customer-backend:latest
        ports:
        - containerPort: 3001
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: customer-secrets
              key: database-url
```

## Monitoring and Logging

### Health Checks
```bash
# Backend health
curl http://localhost:3001/health

# Database connection
curl http://localhost:3001/health/db

# Redis connection (if configured)
curl http://localhost:3001/health/redis
```

### Logs
```bash
# View all service logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f customer-backend
docker-compose logs -f customer-embed
```

## Security Configuration

### CORS Settings
Update `backend/src/main.ts`:
```typescript
origin: [
  'https://your-finance-app.com',
  'https://your-embed-domain.com',
  'https://checkout.yourdomain.com'
]
```

### Content Security Policy
For iframe embedding, ensure parent sites have:
```html
<meta http-equiv="Content-Security-Policy" 
      content="frame-src 'self' https://embed.yourdomain.com;">
```

## Scaling Considerations

### Load Balancing
- Use nginx or cloud load balancer
- Configure session affinity if needed
- Enable health checks on `/health`

### Database Scaling
- Use read replicas for queries
- Implement connection pooling
- Consider database sharding for large datasets

### Caching
- Redis for session storage
- CDN for static assets
- API response caching

## Backup and Recovery

### Database Backup
```bash
# Automated backup script
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d_%H%M%S).sql

# Restore from backup
psql $DATABASE_URL < backup_file.sql
```

### Application Backup
```bash
# Backup configuration and data
tar -czf customer-backup-$(date +%Y%m%d).tar.gz \
  backend/.env \
  customer-embed/.env.local \
  docker-compose.yml
```

## Troubleshooting

### Common Issues

1. **Port conflicts**
   ```bash
   # Check port usage
   netstat -tulpn | grep :3001
   
   # Kill process on port
   kill -9 $(lsof -t -i:3001)
   ```

2. **Database connection issues**
   ```bash
   # Test database connection
   psql $DATABASE_URL -c "SELECT 1;"
   
   # Reset database
   npx prisma migrate reset
   ```

3. **CORS errors**
   - Check allowed origins in backend configuration
   - Verify iframe embedding headers
   - Test with browser dev tools

### Debug Mode
```bash
# Enable debug logging
export LOG_LEVEL=debug
export DEBUG=*

# Run with verbose output
npm run dev -- --verbose
```

## Performance Optimization

### Frontend
- Enable Next.js static optimization
- Use CDN for assets
- Implement code splitting

### Backend
- Enable response compression
- Use database indexes
- Implement API rate limiting

### Database
- Optimize queries with EXPLAIN
- Use connection pooling
- Regular VACUUM and ANALYZE

---

**Support**: Check logs, health endpoints, and API documentation for troubleshooting.
