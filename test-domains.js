// Test script to verify custom domains and production backend connectivity
const https = require('https');
const http = require('http');

// Test configurations
const tests = [
  {
    name: 'Local Custom Domain - Admin',
    url: 'http://ng-customer-admin-local.dev1.ngnair.com:3062',
    expected: 'Should resolve to localhost with custom domain'
  },
  {
    name: 'Local Custom Domain - Embed',
    url: 'http://ng-customer-fe-local.dev1.ngnair.com:3061',
    expected: 'Should resolve to localhost with custom domain'
  },
  {
    name: 'Local Custom Domain - Backend',
    url: 'http://ng-customer-local.dev1.ngnair.com:3060/health',
    expected: 'Should resolve to localhost backend'
  },
  {
    name: 'Production Backend',
    url: 'https://ng-customer-dev.dev1.ngnair.com/health',
    expected: 'Should connect to production backend'
  },
  {
    name: 'Production Backend Auth',
    url: 'https://ng-customer-dev.dev1.ngnair.com/auth/status',
    expected: 'Should connect to production auth endpoint'
  }
];

// Function to test a URL
function testUrl(test) {
  return new Promise((resolve) => {
    const isHttps = test.url.startsWith('https');
    const client = isHttps ? https : http;
    
    console.log(`\n🧪 Testing: ${test.name}`);
    console.log(`📍 URL: ${test.url}`);
    console.log(`📝 Expected: ${test.expected}`);
    
    const startTime = Date.now();
    
    const req = client.get(test.url, { timeout: 5000 }, (res) => {
      const duration = Date.now() - startTime;
      console.log(`✅ Status: ${res.statusCode}`);
      console.log(`⏱️  Response time: ${duration}ms`);
      console.log(`🌐 Headers: ${JSON.stringify(res.headers, null, 2)}`);
      
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        if (data) {
          console.log(`📄 Response: ${data.substring(0, 200)}${data.length > 200 ? '...' : ''}`);
        }
        resolve({ success: true, status: res.statusCode, duration, data });
      });
    });
    
    req.on('error', (error) => {
      const duration = Date.now() - startTime;
      console.log(`❌ Error: ${error.message}`);
      console.log(`⏱️  Duration: ${duration}ms`);
      resolve({ success: false, error: error.message, duration });
    });
    
    req.on('timeout', () => {
      req.destroy();
      console.log(`⏰ Timeout after 5 seconds`);
      resolve({ success: false, error: 'Timeout', duration: 5000 });
    });
  });
}

// Run all tests
async function runTests() {
  console.log('🚀 Starting Domain and Backend Connectivity Tests');
  console.log('=' .repeat(60));
  
  const results = [];
  
  for (const test of tests) {
    const result = await testUrl(test);
    results.push({ ...test, ...result });
    console.log('-'.repeat(60));
  }
  
  // Summary
  console.log('\n📊 TEST SUMMARY');
  console.log('=' .repeat(60));
  
  results.forEach(result => {
    const status = result.success ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${result.name}`);
    if (!result.success) {
      console.log(`   Error: ${result.error}`);
    }
  });
  
  const passCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  console.log(`\n🎯 Results: ${passCount}/${totalCount} tests passed`);
  
  if (passCount === totalCount) {
    console.log('🎉 All tests passed! Configuration is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Check the configuration and network connectivity.');
  }
}

// Run the tests
runTests().catch(console.error);
