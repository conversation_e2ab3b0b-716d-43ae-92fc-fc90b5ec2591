'use client';
// import { TabLinks } from '@/components/globals';
import { ComingSoon } from '@/components/globals/coming-soon';
// import { CustomerPages } from '@/components/globals/Headerv3';
import { env } from 'next-runtime-env';
import React from 'react';

export default function Page({ children }: { children: React.ReactNode }) {
  const isProd = env('NEXT_PUBLIC_COMING_SOON_CUSTOMERS') === 'true';
  return (
    <>
      {/* Temporarily disabled customer functionality */}
      {/* <TabLinks items={CustomerPages} /> */}
      {isProd && <ComingSoon />}
      {!isProd && <div className="mx-4">{children}</div>}
    </>
  );
}
