# Authentication Module

This module provides a shared authentication system that can be integrated into any frontend and backend service.

## Overview

The authentication system uses encrypted cookies with JWT tokens and integrates with an external auth service.

## Flow

1. **Check Authentication**: System checks for `access_token` and `refresh_token` cookies
2. **Debug Mode (Optional)**: If `NEXT_PUBLIC_AUTH_DEBUG_MODE=true`, show debugging information before redirect
3. **Redirect to Auth**: If no valid tokens, redirect to `https://ng-auth-fe-dev.dev1.ngnair.com/login?redirect={current_url}`
4. **Receive Tokens**: After login, auth service redirects back with encrypted cookies
5. **Decrypt Tokens**: Backend decrypts the cookies using AES-256-GCM encryption
6. **Verify JWT**: Verify JWT tokens using JWKS from `https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks`

## Debug Mode

When `NEXT_PUBLIC_AUTH_DEBUG_MODE=true` is set in the frontend environment, the authentication system will:

1. **Display Cookie Status**: Show whether access_token and refresh_token cookies are present
2. **Manual Redirect**: Instead of automatically redirecting to auth login, show a button for manual redirect
3. **Debug Information**: Display helpful debugging information about the authentication state

This is useful for development and troubleshooting authentication issues.

## Integration

### Frontend Integration
Copy the `frontend/` folder to your frontend project and:

1. **Install dependencies:**
   ```bash
   npm install node-rails-message-encryptor
   ```

2. **Add environment variables to `.env`:**
   ```env
   NEXT_PUBLIC_AUTH_FRONTEND_URL=https://ng-auth-fe-dev.dev1.ngnair.com
   NEXT_PUBLIC_AUTH_JWKS_URL=https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks
   ACCESS_TOKEN_ENCRYPTION_KEY=b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8
   ```

3. **Import and use the auth components:**
   ```typescript
   // In your main app file (e.g., _app.tsx or layout.tsx)
   import { AuthProvider } from './auth';

   export default function App({ Component, pageProps }) {
     return (
       <AuthProvider>
         <Component {...pageProps} />
       </AuthProvider>
     );
   }

   // In your components
   import { useAuth, ProtectedRoute } from './auth';

   function MyComponent() {
     const { user, isAuthenticated, login, logout } = useAuth();

     if (!isAuthenticated) {
       return <button onClick={() => login()}>Login</button>;
     }

     return (
       <div>
         <p>Welcome, {user?.email}</p>
         <button onClick={logout}>Logout</button>
       </div>
     );
   }

   // For protected routes
   function ProtectedPage() {
     return (
       <ProtectedRoute requiredRole="admin">
         <AdminContent />
       </ProtectedRoute>
     );
   }
   ```

### Backend Integration
Copy the `backend/` folder to your backend project and:

1. **Install dependencies:**
   ```bash
   npm install jsonwebtoken jwks-client @fastify/cookie class-validator
   ```

   **Add to package.json:**
   ```json
   {
     "dependencies": {
       "@fastify/cookie": "^10.0.1",
       "jsonwebtoken": "^9.0.0",
       "jwks-client": "^3.0.1",
       "class-validator": "^0.14.0"
     }
   }
   ```

2. **Add environment variables to `.env`:**
   ```env
   AUTH_FRONTEND_URL=https://ng-auth-fe-dev.dev1.ngnair.com
   AUTH_JWKS_URL=https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks
   ACCESS_TOKEN_ENCRYPTION_KEY=b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8
   ```

3. **Add the auth module to your app.module.ts:**
   ```typescript
   import { AuthModule } from './modules/auth/auth.module';

   @Module({
     imports: [
       // ... other modules
       AuthModule,
     ],
     // ...
   })
   export class AppModule {}
   ```

4. **Add cookie support to your main.ts:**
   ```typescript
   // Register cookie plugin for cookie parsing
   await app.register(require('@fastify/cookie'), {
     secret: process.env.COOKIE_SECRET || 'your-secret-key-here',
   });
   ```

5. **Add cookie authentication to Swagger (optional):**
   ```typescript
   const config = new DocumentBuilder()
     .setTitle('Your API')
     .setDescription('API Documentation')
     .setVersion('1.0')
     .addCookieAuth('access_token', {
       type: 'apiKey',
       in: 'cookie',
       name: 'access_token',
       description: 'Access token stored in cookie'
     })
     .addCookieAuth('refresh_token', {
       type: 'apiKey',
       in: 'cookie',
       name: 'refresh_token',
       description: 'Refresh token stored in cookie'
     })
     .build();
   ```

6. **Use auth guards and decorators in your controllers:**
   ```typescript
   import { Controller, Get, UseGuards } from '@nestjs/common';
   import { AuthGuard } from './modules/auth/auth.guard';

   @Controller('protected')
   export class ProtectedController {

     @Get('user-data')
     @UseGuards(AuthGuard)
     getUserData(@Req() request) {
       const user = request.user; // User attached by auth middleware
       return { message: `Hello ${user.email}` };
     }
   }
   ```

## API Endpoints

The auth module provides the following endpoints:

### Authentication Endpoints

- **GET `/auth/me/cookies`** - Get current user from encrypted cookies
  - Returns: User object with profile information
  - Status: 200 (success) or 401 (unauthorized)

- **POST `/auth/refresh`** - Refresh authentication tokens
  - Returns: Success status
  - Status: 200 (success) or 401 (unauthorized)

- **GET `/auth/me`** - Legacy endpoint (redirects to `/auth/me/cookies`)
  - Returns: User object with profile information
  - Status: 200 (success) or 401 (unauthorized)

- **GET `/auth/status`** - Public endpoint to check auth service status
  - Returns: Service status information
  - Status: 200 (always accessible)

- **POST `/auth/decrypt`** - Decrypt encrypted token (testing endpoint)
  - Body: `{ "token": "encrypted_token_here" }`
  - Returns: Decrypted token with type information
  - Status: 200 (success) or 400 (invalid format)

- **POST `/auth/decode-jwt`** - Decode JWT token (testing endpoint)
  - Body: `{ "jwt": "jwt_token_here" }`
  - Returns: JWT header, payload, and signature
  - Status: 200 (success) or 400 (invalid JWT)

### Example Response

```json
{
  "id": "user123",
  "email": "<EMAIL>",
  "username": "johndoe",
  "firstName": "John",
  "lastName": "Doe",
  "role": "user",
  "permissions": ["read:profile", "write:profile"],
  "createdAt": "2025-01-01T00:00:00Z",
  "updatedAt": "2025-01-01T00:00:00Z"
}
```

## Environment Variables

### Backend (.env)
```env
# Local Domain Configuration (for development)
LOCAL_BACKEND_DOMAIN=http://ng-customer-local.dev1.ngnair.com:3060
LOCAL_FRONTEND_DOMAIN=http://ng-customer-fe-local.dev1.ngnair.com:3061
LOCAL_ADMIN_DOMAIN=http://ng-customer-admin-local.dev1.ngnair.com:3062

# Auth Configuration
AUTH_FRONTEND_URL=https://ng-auth-fe-dev.dev1.ngnair.com
AUTH_JWKS_URL=https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks
ACCESS_TOKEN_ENCRYPTION_KEY=b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8
CORS_ORIGINS=http://ng-customer-admin-local.dev1.ngnair.com:3062,http://ng-customer-fe-local.dev1.ngnair.com:3061

# Application Settings
SOURCE_IP=0.0.0.0
CORS_SUBDOMAIN=dev1
PORT=3060
NODE_ENV=development
```

### Frontend (.env)
```env
# Local Domain Configuration (for local development hosting)
NEXT_PUBLIC_LOCAL_BACKEND_DOMAIN=http://ng-customer-local.dev1.ngnair.com:3060
NEXT_PUBLIC_LOCAL_FRONTEND_DOMAIN=http://ng-customer-fe-local.dev1.ngnair.com:3061
NEXT_PUBLIC_LOCAL_ADMIN_DOMAIN=http://ng-customer-admin-local.dev1.ngnair.com:3062

# Auth Configuration
NEXT_PUBLIC_AUTH_FRONTEND_URL=https://ng-auth-fe-dev.dev1.ngnair.com
NEXT_PUBLIC_AUTH_JWKS_URL=https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks
ACCESS_TOKEN_ENCRYPTION_KEY=b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8

# Production Backend API Configuration (connect to production backend)
NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL=https://ng-customer-dev.dev1.ngnair.com/graphql
NEXT_PUBLIC_CUSTOMER_API_URL=https://ng-customer-dev.dev1.ngnair.com

# Auth Debug Mode - Set to true to enable debugging before auth redirect
NEXT_PUBLIC_AUTH_DEBUG_MODE=true

# Application Settings
PORT=3061  # or 3062 for admin
NODE_ENV=development
```

## Configuration Strategy

This setup allows you to:

1. **Host locally with custom domains**: Use `ng-customer-*-local.dev1.ngnair.com` domains for local development instead of localhost
2. **Connect to production backend**: Frontend connects to `https://ng-customer-dev.dev1.ngnair.com` for API calls
3. **Test both configurations**: You can test both local custom domains and production backend connectivity
4. **Easy domain switching**: Change domains by updating environment variables only

### Testing Custom Domains

To test if custom local domains are working:
1. Add entries to your hosts file (Windows: `C:\Windows\System32\drivers\etc\hosts`):
   ```
   127.0.0.1 ng-customer-local.dev1.ngnair.com
   127.0.0.1 ng-customer-fe-local.dev1.ngnair.com
   127.0.0.1 ng-customer-admin-local.dev1.ngnair.com
   ```
2. Access your applications using the custom domains
3. Check browser console for API calls to verify they're going to production backend

## Files Structure

```
auth/
├── README.md
├── frontend/
│   ├── hooks/
│   │   └── useAuth.ts
│   ├── components/
│   │   ├── AuthProvider.tsx
│   │   └── ProtectedRoute.tsx
│   ├── services/
│   │   └── authService.ts
│   └── types/
│       └── auth.types.ts
└── backend/
    ├── auth.module.ts
    ├── auth.service.ts
    ├── auth.guard.ts
    ├── auth.middleware.ts
    └── types/
        └── auth.types.ts
```
