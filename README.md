# Customer Microservice - No Auth + Embeddable Frontend

A comprehensive customer management microservice with **authentication removed** for testing, featuring an **embeddable customer data collection frontend** for finance integration.

## 🚀 Quick Start

### All Services with Docker
```bash
# Build all services first
docker-compose build

# Start all services
docker-compose up -d

# Access applications
# Backend API: http://localhost:3001
# Admin Panel: http://localhost:3000
# Embed Demo: http://localhost:3002/demo
# Checkout Demo: http://localhost:3002/checkout
```

### 🌱 Seeded Data
The system comes with pre-populated sample data:
- **5 sample customers** with different statuses (Active, Pending, Suspended)
- **Multiple addresses** per customer (Home, Billing, Work)
- **Business and Individual** customer types
- **Verification statuses** (Email, Phone, KYC)
- **Tags and notes** for categorization

### Manual Setup
```bash
# Backend (Port 3001)
cd backend && npm install && npm run build && npm start

# Admin Panel (Port 3000) 
cd customer-admin && npm install && npm run dev

# Embeddable Frontend (Port 3002)
cd customer-embed && npm install && npm run dev
```

## 🎯 Key Features

### ✅ Authentication Removed
- **No login required** - Direct access to all endpoints
- **No JWT tokens** - All APIs accessible without authentication
- **Mock user data** - Admin panel uses test user
- **Open CORS** - Configured for cross-origin requests

### 🎨 Embeddable Customer Frontend
- **iframe-ready** customer data collection form
- **Real-time tax calculation** via address input
- **PostMessage communication** with parent window
- **Responsive design** with Tailwind CSS
- **Finance integration ready**

### 🛒 Checkout Demo Integration
- **Customer selection** from seeded database
- **Real-time customer data** display
- **Tax calculation** based on customer address
- **Order summary** with dynamic totals
- **Complete checkout flow** demonstration
- **Finance system integration** example

### 🔧 Complete API Access
- **REST APIs** - Full CRUD operations without auth
- **GraphQL** - Query and mutation support
- **Admin operations** - Customer management functions
- **Swagger docs** - Interactive API documentation

## 📊 Architecture

```
Finance Frontend → Customer Embed (iframe) → Customer API → Tax Service
                ↓                        ↓
            Customer Data            Tax Calculation
                ↓                        ↓
            Finance Backend ← Kafka ← Customer Service
```

## 🔗 Finance Integration

### Embed in Your Checkout
```html
<iframe 
  src="http://localhost:3002/?amount=100.00"
  width="100%" 
  height="600"
  frameborder="0">
</iframe>
```

### Capture Customer Data
```javascript
window.addEventListener('message', (event) => {
  if (event.data.type === 'CUSTOMER_CREATED') {
    // Send to your finance backend
    handleCustomerData(event.data.customer);
  }
  if (event.data.type === 'TAX_CALCULATED') {
    // Update checkout total
    updateTotal(event.data.tax);
  }
});
```

### 🛒 Checkout Demo Workflow

The checkout demo (`http://localhost:3002/checkout`) demonstrates the complete integration:

1. **Customer Selection**: Choose from seeded customer database
2. **Real-time Display**: Customer details and addresses shown
3. **Tax Calculation**: Automatic tax calculation based on customer address
4. **Order Processing**: Complete checkout flow with customer data
5. **Integration Example**: Shows how customer data flows to finance systems

**Key Features:**
- ✅ Displays all seeded customers with different statuses
- ✅ Shows customer verification states (Email, Phone, KYC)
- ✅ Real-time tax calculation based on customer address
- ✅ Complete order summary with dynamic totals
- ✅ Demonstrates finance system integration patterns

## 📁 Project Structure

```
customer/
├── backend/                 # NestJS API (No Auth)
├── customer-admin/          # React Admin Panel (No Auth)
├── customer-embed/          # Embeddable Frontend (NEW)
├── customer-style/          # Shared UI Components
├── FINANCE_INTEGRATION_GUIDE.md
├── IMPLEMENTATION_SUMMARY.md
└── docker-compose.yml
```

## 🧪 Testing

### API Testing (No Auth Required)
```bash
# Test all endpoints
node test-api.js

# Manual API test
curl http://localhost:3001/customers
curl -X POST http://localhost:3001/customers -H "Content-Type: application/json" -d '{"firstName":"John","lastName":"Doe","email":"<EMAIL>"}'
```

### Frontend Testing
- **Admin Panel**: http://localhost:3000 (auto-redirects to dashboard)
- **Embed Demo**: http://localhost:3002/demo (live integration example)
- **Checkout Demo**: http://localhost:3002/checkout (complete integration demo)
- **API Docs**: http://localhost:3001/api (Swagger documentation)

### ✅ Test Results
- ✅ **Health Check**: Backend API responding correctly
- ✅ **Customer Creation**: POST /customers working
- ✅ **Customer Listing**: GET /customers returning seeded data
- ✅ **GraphQL Queries**: All GraphQL endpoints functional
- ✅ **Frontend Integration**: Customer-embed displaying data correctly
- ✅ **Checkout Flow**: Complete checkout demo working
- ✅ **Docker Build**: All services building and running successfully

### ⚠️ Known Issues
- **UUID Validation**: REST endpoints expect UUID format but database generates CUIDs
  - **Impact**: Some REST endpoints (GET/PUT/DELETE by ID) return validation errors
  - **Workaround**: Use GraphQL endpoints or customer listing for data access
  - **Status**: Functional issue, doesn't affect core integration workflow

## 📋 Available Endpoints (No Auth)

### Customer Management
- `GET /customers` - List all customers
- `POST /customers` - Create customer
- `GET /customers/:id` - Get customer details
- `PUT /customers/:id` - Update customer
- `DELETE /customers/:id` - Delete customer

### Admin Operations
- `POST /customers/:id/suspend` - Suspend customer
- `POST /customers/:id/activate` - Activate customer
- `PUT /customers/:id/admin-update` - Admin update

### GraphQL
- **Playground**: http://localhost:3001/graphql
- **Queries**: customers, customer, customerByEmail
- **Mutations**: createCustomer, updateCustomer, deleteCustomer

## 🎨 Embeddable Frontend Features

### Customer Form
- **Personal Info**: Name, email, phone
- **Address**: Street, city, state, ZIP, country
- **Real-time Validation**: Form validation with error messages
- **Tax Calculation**: Automatic tax computation based on address

### Integration Options
- **Standalone**: Direct URL with query parameters
- **iframe Embed**: Full iframe integration
- **PostMessage API**: Parent-child communication
- **Customizable**: Theme and behavior configuration

## 🔧 Configuration

### Backend Environment
```bash
PORT=3001
DATABASE_URL=postgresql://nestjs:password@localhost:5432/customer_service
ALLOWED_ORIGIN_1=http://localhost:3000
ALLOWED_ORIGIN_2=http://localhost:3002
NODE_ENV=development
```

### Embed Environment
```bash
NEXT_PUBLIC_API_BASE_URL=http://localhost:3001/api/v1
```

## 📖 Documentation

- **[Finance Integration Guide](FINANCE_INTEGRATION_GUIDE.md)** - Complete integration instructions
- **[Implementation Summary](IMPLEMENTATION_SUMMARY.md)** - Technical details and changes
- **[Deployment Guide](DEPLOYMENT_GUIDE.md)** - Production deployment instructions
- **[Customer Embed README](customer-embed/README.md)** - Embed-specific documentation

## 🚀 Next Steps for Finance Team

1. **Review Integration Guide** - Read `FINANCE_INTEGRATION_GUIDE.md`
2. **Test Demo** - Visit http://localhost:3002/demo
3. **Integrate iframe** - Add to your checkout page
4. **Implement listeners** - Capture customer and tax data
5. **Connect backend** - Process customer data in your system

## 🔒 Security Notes

- **Authentication disabled** for testing purposes only
- **CORS enabled** for cross-origin embedding
- **PostMessage** used for secure iframe communication
- **Input validation** maintained on backend
- **Re-enable auth** before production deployment

## 📞 Support

- **Demo**: http://localhost:3002/demo
- **API Docs**: http://localhost:3001/api  
- **GraphQL**: http://localhost:3001/graphql
- **Health Check**: http://localhost:3001/health

---

**Status**: ✅ Ready for finance integration testing
**Auth Status**: 🔓 Disabled for testing
**Embed Status**: 🎯 Ready for iframe integration
