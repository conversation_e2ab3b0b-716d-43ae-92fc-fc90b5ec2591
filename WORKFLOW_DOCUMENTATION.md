# Customer-Finance Integration Workflow

## Overview

This document describes the complete workflow for integrating the customer data collection system with the finance microservice for seamless checkout processing.

## 🔄 Complete Data Flow

### 1. Customer Checkout Initiation
```
Finance Frontend (Checkout Page)
    ↓
Embeds Customer iframe
    ↓
Customer fills form with address
    ↓
Real-time tax calculation
    ↓
Customer data submission
    ↓
PostMessage to Finance Frontend
    ↓
Finance processes payment
    ↓
Customer record updated via Kafka
```

## 📋 Step-by-Step Implementation

### Phase 1: Finance Frontend Integration

#### 1.1 Embed Customer Form
```html
<!-- In your checkout page -->
<div id="customer-section">
  <h3>Customer Information</h3>
  <iframe 
    id="customer-iframe"
    src="http://localhost:3002/?amount=PURCHASE_AMOUNT"
    width="100%" 
    height="600"
    frameborder="0"
    title="Customer Information">
  </iframe>
</div>
```

#### 1.2 Implement Data Listeners
```javascript
// In your checkout JavaScript
let customerData = null;
let taxData = null;

window.addEventListener('message', (event) => {
  // Security check
  if (event.origin !== 'http://localhost:3002') return;
  
  switch (event.data.type) {
    case 'CUSTOMER_EMBED_READY':
      // Send configuration to iframe
      sendConfigToIframe();
      break;
      
    case 'CUSTOMER_CREATED':
      customerData = event.data.customer;
      console.log('Customer created:', customerData);
      updateCheckoutUI();
      break;
      
    case 'TAX_CALCULATED':
      taxData = event.data.tax;
      console.log('Tax calculated:', taxData);
      updateTotalAmount();
      break;
      
    case 'CUSTOMER_ERROR':
      console.error('Customer form error:', event.data.error);
      showErrorMessage(event.data.error);
      break;
  }
});

function sendConfigToIframe() {
  const iframe = document.getElementById('customer-iframe');
  iframe.contentWindow.postMessage({
    type: 'CUSTOMER_EMBED_CONFIG',
    config: {
      apiBaseUrl: 'http://localhost:3001/api/v1',
      theme: {
        primaryColor: '#your-brand-color'
      }
    },
    purchaseAmount: getCurrentPurchaseAmount()
  }, '*');
}
```

#### 1.3 Update Checkout UI
```javascript
function updateCheckoutUI() {
  if (customerData) {
    // Show customer summary
    document.getElementById('customer-name').textContent = 
      `${customerData.firstName} ${customerData.lastName}`;
    document.getElementById('customer-email').textContent = 
      customerData.email;
    
    // Enable payment section
    document.getElementById('payment-section').style.display = 'block';
  }
}

function updateTotalAmount() {
  if (taxData) {
    document.getElementById('subtotal').textContent = 
      `$${(taxData.totalAmount - taxData.taxAmount).toFixed(2)}`;
    document.getElementById('tax-amount').textContent = 
      `$${taxData.taxAmount.toFixed(2)}`;
    document.getElementById('total-amount').textContent = 
      `$${taxData.totalAmount.toFixed(2)}`;
  }
}
```

### Phase 2: Finance Backend Integration

#### 2.1 Create Customer Endpoint
```javascript
// In your finance backend
app.post('/api/finance/customer', async (req, res) => {
  try {
    const customerData = req.body;
    
    // Store customer data in your finance database
    const financeCustomer = await FinanceCustomer.create({
      customerId: customerData.id,
      firstName: customerData.firstName,
      lastName: customerData.lastName,
      email: customerData.email,
      phone: customerData.phone,
      // Map other fields as needed
    });
    
    res.json({ success: true, financeCustomerId: financeCustomer.id });
  } catch (error) {
    console.error('Error storing customer:', error);
    res.status(500).json({ error: 'Failed to store customer data' });
  }
});
```

#### 2.2 Process Payment with Customer Data
```javascript
app.post('/api/finance/process-payment', async (req, res) => {
  try {
    const { customerId, paymentMethod, amount, taxData } = req.body;
    
    // Process payment
    const payment = await PaymentProcessor.charge({
      amount: taxData.totalAmount,
      currency: 'USD',
      customer: customerId,
      paymentMethod: paymentMethod,
      metadata: {
        subtotal: amount,
        tax: taxData.taxAmount,
        taxRate: taxData.taxRate
      }
    });
    
    // Update customer record with payment method
    await updateCustomerPaymentMethod(customerId, paymentMethod);
    
    res.json({ success: true, paymentId: payment.id });
  } catch (error) {
    console.error('Payment processing failed:', error);
    res.status(500).json({ error: 'Payment failed' });
  }
});
```

### Phase 3: Kafka Integration (Optional)

#### 3.1 Customer Service Events
```javascript
// In customer microservice
const kafka = require('kafkajs');

class CustomerEventPublisher {
  async publishCustomerCreated(customer) {
    await this.producer.send({
      topic: 'customer-events',
      messages: [{
        key: customer.id,
        value: JSON.stringify({
          eventType: 'CUSTOMER_CREATED',
          customerId: customer.id,
          data: customer,
          timestamp: new Date().toISOString()
        })
      }]
    });
  }
  
  async publishPaymentMethodAdded(customerId, paymentMethod) {
    await this.producer.send({
      topic: 'customer-events',
      messages: [{
        key: customerId,
        value: JSON.stringify({
          eventType: 'PAYMENT_METHOD_ADDED',
          customerId: customerId,
          data: { paymentMethod },
          timestamp: new Date().toISOString()
        })
      }]
    });
  }
}
```

#### 3.2 Finance Service Consumer
```javascript
// In finance backend
class CustomerEventConsumer {
  async handleCustomerCreated(event) {
    const { customerId, data } = event;
    
    // Sync customer data to finance database
    await FinanceCustomer.upsert({
      customerId: customerId,
      ...data,
      syncedAt: new Date()
    });
  }
  
  async handlePaymentMethodAdded(event) {
    const { customerId, data } = event;
    
    // Update customer payment methods
    await FinanceCustomer.update(
      { paymentMethods: data.paymentMethod },
      { where: { customerId } }
    );
  }
}
```

## 🔄 OTP MFA for Payment Method Reuse

### 1. Check Existing Customer
```javascript
// In customer embed form
async function checkExistingCustomer(email) {
  try {
    const response = await fetch(`${API_BASE}/customers/by-email/${email}`);
    if (response.ok) {
      const customer = await response.json();
      if (customer.paymentMethods?.length > 0) {
        showPaymentMethodSelection(customer);
        return true;
      }
    }
    return false;
  } catch (error) {
    console.error('Error checking customer:', error);
    return false;
  }
}
```

### 2. OTP Verification Flow
```javascript
// Send OTP
async function sendOTP(customerId, phone) {
  const response = await fetch(`${API_BASE}/customers/${customerId}/send-otp`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ phone })
  });
  return response.ok;
}

// Verify OTP
async function verifyOTP(customerId, otp) {
  const response = await fetch(`${API_BASE}/customers/${customerId}/verify-otp`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ otp })
  });
  
  if (response.ok) {
    const result = await response.json();
    return result.verified;
  }
  return false;
}
```

## 📊 Data Structures

### Customer Data Format
```typescript
interface CustomerData {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  status: 'ACTIVE' | 'SUSPENDED' | 'BLOCKED';
  addresses: Address[];
  paymentMethods?: PaymentMethod[];
  createdAt: string;
  updatedAt: string;
}
```

### Tax Data Format
```typescript
interface TaxData {
  taxAmount: number;
  taxRate: number;
  totalAmount: number;
  breakdown: {
    stateTax: number;
    localTax: number;
    federalTax: number;
  };
}
```

## 🔧 Error Handling

### Frontend Error Handling
```javascript
function handleCustomerError(error) {
  // Show user-friendly error
  showNotification('error', 'Unable to save customer information. Please try again.');
  
  // Log for debugging
  console.error('Customer form error:', error);
  
  // Optionally retry or provide alternative
  if (error.includes('network')) {
    showRetryOption();
  }
}
```

### Backend Error Handling
```javascript
// In customer API
app.use((error, req, res, next) => {
  console.error('Customer API Error:', error);
  
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      error: 'Invalid customer data',
      details: error.details
    });
  }
  
  if (error.name === 'DatabaseError') {
    return res.status(500).json({
      error: 'Database error',
      message: 'Please try again later'
    });
  }
  
  res.status(500).json({
    error: 'Internal server error'
  });
});
```

## 🚀 Production Considerations

### Security
- Enable HTTPS for all communications
- Validate all PostMessage origins
- Implement rate limiting on APIs
- Use secure payment processing

### Performance
- Implement caching for tax calculations
- Use CDN for static assets
- Optimize database queries
- Monitor API response times

### Monitoring
- Track customer creation success rates
- Monitor tax calculation accuracy
- Log payment processing metrics
- Set up alerts for failures

---

**Next Steps**: Follow this workflow to implement the complete customer-finance integration with real-time data flow and payment processing.
