import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as jwt from 'jsonwebtoken';
import * as jwksClient from 'jwks-client';
import * as crypto from 'crypto';
import { User, JWTPayload, AuthConfig, DecryptedTokens, JWKSResponse } from './types/auth.types';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  private readonly config: AuthConfig;
  private jwksClientInstance: jwksClient.JwksClient;

  constructor(private configService: ConfigService) {
    this.config = {
      authJwksUrl: this.configService.get<string>('AUTH_JWKS_URL') || 'https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks',
      encryptionKey: this.configService.get<string>('ACCESS_TOKEN_ENCRYPTION_KEY') || 'b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8',
      cookieNames: {
        accessToken: 'access_token',
        refreshToken: 'refresh_token',
      },
    };

    // Initialize JWKS client
    this.jwksClientInstance = jwksClient({
      jwksUri: this.config.authJwksUrl,
      requestHeaders: {},
      timeout: 30000,
    });
  }

  /**
   * Decrypt cookies using Rails-compatible encryption (AES-256-GCM)
   */
  private decryptCookie(encryptedValue: string): string {
    try {
      this.logger.log(`Attempting to decrypt token: ${encryptedValue.substring(0, 50)}...`);

      // URL decode the cookie value
      const decodedValue = decodeURIComponent(encryptedValue);
      this.logger.log(`URL decoded length: ${decodedValue.length}`);

      // Rails MessageEncryptor format: base64(encrypted_data)--base64(iv)--base64(auth_tag)
      const parts = decodedValue.split('--');
      this.logger.log(`Split into ${parts.length} parts`);

      if (parts.length !== 3) {
        this.logger.error(`Invalid encrypted cookie format. Expected 3 parts, got ${parts.length}`);
        throw new Error('Invalid encrypted cookie format');
      }

      const encryptedData = Buffer.from(parts[0], 'base64');
      const iv = Buffer.from(parts[1], 'base64');
      const authTag = Buffer.from(parts[2], 'base64');

      // Convert hex key to buffer
      const key = Buffer.from(this.config.encryptionKey, 'hex');

      // Create decipher
      const decipher = crypto.createDecipherGCM('aes-256-gcm', key);
      decipher.setIV(iv);
      decipher.setAuthTag(authTag);

      // Decrypt
      let decrypted = decipher.update(encryptedData, undefined, 'utf8');
      decrypted += decipher.final('utf8');

      return decrypted;
    } catch (error) {
      this.logger.error('Failed to decrypt cookie:', error);
      throw new UnauthorizedException('Invalid encrypted token');
    }
  }

  /**
   * Extract and decrypt tokens from cookies
   */
  extractTokensFromCookies(cookies: Record<string, string>): DecryptedTokens {
    const encryptedAccessToken = cookies[this.config.cookieNames.accessToken];
    const encryptedRefreshToken = cookies[this.config.cookieNames.refreshToken];

    if (!encryptedAccessToken || !encryptedRefreshToken) {
      throw new UnauthorizedException('Missing authentication tokens');
    }

    try {
      const accessToken = this.decryptCookie(encryptedAccessToken);
      const refreshToken = this.decryptCookie(encryptedRefreshToken);

      return { accessToken, refreshToken };
    } catch (error) {
      this.logger.error('Failed to decrypt tokens:', error);
      throw new UnauthorizedException('Invalid authentication tokens');
    }
  }

  /**
   * Get signing key from JWKS
   */
  private async getSigningKey(kid: string): Promise<string> {
    try {
      const key = await this.jwksClientInstance.getSigningKey(kid);
      return key.getPublicKey();
    } catch (error) {
      this.logger.error('Failed to get signing key:', error);
      throw new UnauthorizedException('Failed to verify token signature');
    }
  }

  /**
   * Verify JWT token using JWKS
   */
  async verifyToken(token: string): Promise<JWTPayload> {
    try {
      // Decode token header to get kid
      const decoded = jwt.decode(token, { complete: true });
      
      if (!decoded || !decoded.header.kid) {
        throw new UnauthorizedException('Invalid token format');
      }

      // Get signing key
      const signingKey = await this.getSigningKey(decoded.header.kid);

      // Verify token
      const payload = jwt.verify(token, signingKey, {
        algorithms: ['RS256'],
      }) as JWTPayload;

      return payload;
    } catch (error) {
      this.logger.error('Token verification failed:', error);
      throw new UnauthorizedException('Invalid or expired token');
    }
  }

  /**
   * Get user from JWT payload
   */
  getUserFromPayload(payload: JWTPayload): User {
    return {
      id: payload.sub,
      email: payload.email,
      username: payload.username,
      firstName: payload.firstName,
      lastName: payload.lastName,
      role: payload.role,
      permissions: payload.permissions || [],
      createdAt: new Date(payload.iat * 1000).toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }

  /**
   * Authenticate user from cookies
   */
  async authenticateFromCookies(cookies: Record<string, string>): Promise<User> {
    try {
      // Extract and decrypt tokens
      const { accessToken } = this.extractTokensFromCookies(cookies);

      // Verify access token
      const payload = await this.verifyToken(accessToken);

      // Return user info
      return this.getUserFromPayload(payload);
    } catch (error) {
      this.logger.error('Authentication failed:', error);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  /**
   * Refresh authentication using refresh token
   */
  async refreshAuthentication(cookies: Record<string, string>): Promise<boolean> {
    try {
      // Extract and decrypt tokens
      const { refreshToken } = this.extractTokensFromCookies(cookies);

      // Verify refresh token
      await this.verifyToken(refreshToken);

      // In a real implementation, you would call the auth service to get new tokens
      // and set new cookies. For now, we just verify that the refresh token is valid
      return true;
    } catch (error) {
      this.logger.error('Token refresh failed:', error);
      return false;
    }
  }

  /**
   * Check if user has required permissions
   */
  hasPermissions(user: User, requiredPermissions: string[]): boolean {
    if (!user.permissions) return false;
    return requiredPermissions.every(permission => user.permissions!.includes(permission));
  }

  /**
   * Check if user has required role
   */
  hasRole(user: User, requiredRole: string): boolean {
    return user.role === requiredRole;
  }

  /**
   * Decrypt token for testing purposes (public method)
   */
  async decryptTokenForTesting(encryptedToken: string): Promise<string> {
    return this.decryptCookie(encryptedToken);
  }
}
