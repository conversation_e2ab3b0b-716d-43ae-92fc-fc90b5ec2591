import { User, AuthConfig } from '../types/auth.types';

export class AuthService {
  private config: AuthConfig;

  constructor(config: AuthConfig) {
    this.config = config;
  }

  /**
   * Check if user has valid authentication cookies
   */
  hasAuthCookies(): boolean {
    if (typeof document === 'undefined') return false;
    
    const cookies = document.cookie.split(';').reduce((acc, cookie) => {
      const [key, value] = cookie.trim().split('=');
      acc[key] = value;
      return acc;
    }, {} as Record<string, string>);

    return !!(cookies[this.config.cookieNames.accessToken] && cookies[this.config.cookieNames.refreshToken]);
  }

  /**
   * Redirect to external auth service for login
   */
  login(redirectUrl?: string): void {
    const currentUrl = redirectUrl || window.location.href;
    const loginUrl = `${this.config.authFrontendUrl}/login?redirect=${encodeURIComponent(currentUrl)}`;
    window.location.href = loginUrl;
  }

  /**
   * Logout by clearing cookies and redirecting to auth service
   */
  logout(): void {
    // Clear cookies
    document.cookie = `${this.config.cookieNames.accessToken}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    document.cookie = `${this.config.cookieNames.refreshToken}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
    
    // Redirect to auth service logout
    const logoutUrl = `${this.config.authFrontendUrl}/logout`;
    window.location.href = logoutUrl;
  }

  /**
   * Get user info from backend (backend will decrypt and verify tokens)
   */
  async getCurrentUser(): Promise<User | null> {
    try {
      const apiUrl = process.env.NODE_ENV === 'production'
        ? 'https://ng-customer-dev.dev1.ngnair.com'
        : process.env.NEXT_PUBLIC_LOCAL_BACKEND_DOMAIN;
      const response = await fetch(`${apiUrl}/auth/me/cookies`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          // Token expired or invalid - in debug mode, don't auto-redirect
          const isDebugMode = process.env.NEXT_PUBLIC_AUTH_DEBUG_MODE === 'true';
          if (!isDebugMode) {
            this.login();
          }
          return null;
        }
        throw new Error('Failed to get user info');
      }

      const user = await response.json();
      return user;
    } catch (error) {
      console.error('Error getting current user:', error);
      return null;
    }
  }

  /**
   * Refresh authentication by calling backend
   */
  async refreshAuth(): Promise<boolean> {
    try {
      const apiUrl = process.env.NODE_ENV === 'production'
        ? 'https://ng-customer-dev.dev1.ngnair.com'
        : process.env.NEXT_PUBLIC_LOCAL_BACKEND_DOMAIN;
      const response = await fetch(`${apiUrl}/auth/refresh`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          // Refresh token expired - in debug mode, don't auto-redirect
          const isDebugMode = process.env.NEXT_PUBLIC_AUTH_DEBUG_MODE === 'true';
          if (!isDebugMode) {
            this.login();
          }
          return false;
        }
        throw new Error('Failed to refresh auth');
      }

      return true;
    } catch (error) {
      console.error('Error refreshing auth:', error);
      return false;
    }
  }
}

// Default configuration - these should be set in your .env file
export const defaultAuthConfig: AuthConfig = {
  authFrontendUrl: process.env.NEXT_PUBLIC_AUTH_FRONTEND_URL || 'https://ng-auth-fe-dev.dev1.ngnair.com',
  authJwksUrl: process.env.NEXT_PUBLIC_AUTH_JWKS_URL || 'https://ng-auth-dev.dev1.ngnair.com/api/v1/jwks',
  encryptionKey: process.env.ACCESS_TOKEN_ENCRYPTION_KEY || 'b7e2c1a4d8f3e6b9c2f7a1e4d3b8c6f2e1a7b4c9d6e3f8a2b5c4d7e1f6a3b2c8',
  cookieNames: {
    accessToken: 'access_token',
    refreshToken: 'refresh_token',
  },
};

// Export singleton instance
export const authService = new AuthService(defaultAuthConfig);
