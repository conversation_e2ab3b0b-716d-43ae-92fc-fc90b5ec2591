'use client';

import React, { ReactNode, useEffect } from 'react';
import { useAuth } from './AuthProvider';

interface ProtectedRouteProps {
  children: ReactNode;
  fallback?: ReactNode;
  requiredRole?: string;
  requiredPermissions?: string[];
}

export function ProtectedRoute({ 
  children, 
  fallback,
  requiredRole,
  requiredPermissions 
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading, user, login } = useAuth();

  useEffect(() => {
    // If not loading and not authenticated, redirect to login
    if (!isLoading && !isAuthenticated) {
      login();
    }
  }, [isLoading, isAuthenticated, login]);

  // Show loading state
  if (isLoading) {
    return fallback || (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Authenticating...</p>
        </div>
      </div>
    );
  }

  // If not authenticated, show debug interface or redirect
  if (!isAuthenticated) {
    // Check if debug mode is enabled
    const isDebugMode = process.env.NEXT_PUBLIC_AUTH_DEBUG_MODE === 'true';

    if (isDebugMode) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
            <div className="text-center mb-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Authentication Debug</h2>
              <p className="text-gray-600">Debug mode is enabled</p>
            </div>

            <div className="space-y-4">
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <h3 className="text-sm font-medium text-red-800 mb-2">Authentication Status</h3>
                <p className="text-sm text-red-700">Not authenticated</p>
                {error && (
                  <p className="text-sm text-red-700 mt-1">Error: {error}</p>
                )}
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <h3 className="text-sm font-medium text-blue-800 mb-2">Cookie Information</h3>
                <p className="text-sm text-blue-700">
                  Access Token: {typeof window !== 'undefined' && document.cookie.includes('access_token') ? '✅ Present' : '❌ Missing'}
                </p>
                <p className="text-sm text-blue-700">
                  Refresh Token: {typeof window !== 'undefined' && document.cookie.includes('refresh_token') ? '✅ Present' : '❌ Missing'}
                </p>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                <h3 className="text-sm font-medium text-yellow-800 mb-2">Debug Actions</h3>
                <div className="space-y-2">
                  <button
                    onClick={() => {
                      if (typeof window !== 'undefined') {
                        console.log('Cookies:', document.cookie);
                        console.log('Auth Error:', (window as any).authError);
                      }
                    }}
                    className="w-full text-left text-sm text-yellow-700 hover:text-yellow-900 underline"
                  >
                    📋 Log cookies to console
                  </button>
                  <button
                    onClick={() => login()}
                    className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
                  >
                    🔐 Go to Login
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return fallback || null;
  }

  // Check role requirement
  if (requiredRole && user?.role !== requiredRole) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
          <p className="text-gray-600">You don't have the required role to access this page.</p>
          <p className="text-sm text-gray-500 mt-2">Required: {requiredRole}, Your role: {user?.role}</p>
        </div>
      </div>
    );
  }

  // Check permissions requirement
  if (requiredPermissions && requiredPermissions.length > 0) {
    const userPermissions = user?.permissions || [];
    const hasAllPermissions = requiredPermissions.every(permission => 
      userPermissions.includes(permission)
    );

    if (!hasAllPermissions) {
      const missingPermissions = requiredPermissions.filter(permission => 
        !userPermissions.includes(permission)
      );

      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
            <p className="text-gray-600">You don't have the required permissions to access this page.</p>
            <p className="text-sm text-gray-500 mt-2">
              Missing permissions: {missingPermissions.join(', ')}
            </p>
          </div>
        </div>
      );
    }
  }

  // All checks passed, render children
  return <>{children}</>;
}

// Higher-order component version
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    requiredRole?: string;
    requiredPermissions?: string[];
    fallback?: ReactNode;
  }
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <ProtectedRoute
        requiredRole={options?.requiredRole}
        requiredPermissions={options?.requiredPermissions}
        fallback={options?.fallback}
      >
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}
