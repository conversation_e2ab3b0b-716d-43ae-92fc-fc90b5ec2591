import { useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useAuth } from '../auth';

export default function HomePage() {
  const router = useRouter();
  const { isAuthenticated, isLoading, login } = useAuth();

  useEffect(() => {
    if (!isLoading) {
      if (isAuthenticated) {
        // Redirect to dashboard if authenticated
        router.push('/dashboard');
      } else {
        // Redirect to auth service for login
        login();
      }
    }
  }, [isAuthenticated, isLoading, router, login]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <Head>
        <title>Customer Management System</title>
        <meta name="description" content="Customer Management System Admin Panel" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="text-center">
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Customer Management System</h1>
        <p className="text-gray-600">
          {isLoading ? 'Checking authentication...' : 'Redirecting...'}
        </p>
      </div>
    </div>
  );
}