# === Stage 1: Builder ===
FROM node:20-alpine AS builder

WORKDIR /app

# Install deps only
COPY package.json package-lock.json ./
RUN npm install

# Copy full source
COPY . .

# Build the Next.js app
RUN npm run build

# === Stage 2: Production Image ===
FROM node:20-alpine AS runner

WORKDIR /app

ENV NODE_ENV production

# Copy the minimal production artifacts from the builder
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/tailwind.config.js ./tailwind.config.js
COPY --from=builder /app/postcss.config.js ./postcss.config.js

EXPOSE 3002

# Start the app
CMD ["npm", "start"]