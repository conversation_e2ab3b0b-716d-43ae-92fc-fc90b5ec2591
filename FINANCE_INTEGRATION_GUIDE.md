# Finance Integration Guide - Customer Data Collection

## Overview

This guide explains how to integrate the customer data collection component with your finance microservice for seamless checkout experience.

## Architecture

```
Finance Frontend → Customer Embed (iframe) → Customer API → Tax Service (Numeral)
                ↓                        ↓
            Customer Data            Tax Calculation
                ↓                        ↓
            Finance Backend ← Kafka ← Customer Service
```

## Quick Integration

### 1. Embed the Customer Form

Add this iframe to your checkout page:

```html
<iframe 
  id="customer-iframe"
  src="http://localhost:3002/?amount=PURCHASE_AMOUNT"
  width="100%" 
  height="600"
  frameborder="0">
</iframe>
```

### 2. Listen for Customer Data

```javascript
window.addEventListener('message', (event) => {
  if (event.origin !== 'http://localhost:3002') return; // Security check
  
  switch (event.data.type) {
    case 'CUSTOMER_CREATED':
      handleCustomerData(event.data.customer);
      break;
    case 'TAX_CALCULATED':
      updateCheckoutTotal(event.data.tax);
      break;
    case 'CUSTOMER_ERROR':
      showError(event.data.error);
      break;
  }
});

function handleCustomerData(customer) {
  // Send to your finance backend
  fetch('/api/finance/customer', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(customer)
  });
}

function updateCheckoutTotal(tax) {
  // Update your checkout UI
  document.getElementById('tax-amount').textContent = `$${tax.taxAmount.toFixed(2)}`;
  document.getElementById('total-amount').textContent = `$${tax.totalAmount.toFixed(2)}`;
}
```

## Data Structures

### Customer Data
```typescript
interface CustomerResponse {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}
```

### Tax Data
```typescript
interface TaxCalculationResponse {
  taxAmount: number;
  taxRate: number;
  totalAmount: number;
  breakdown: {
    stateTax: number;
    localTax: number;
    federalTax: number;
  };
}
```

## API Endpoints

### Customer Service (Port 3001)
- `POST /api/v1/customers` - Create customer
- `PUT /api/v1/customers/:id` - Update customer
- `GET /api/v1/customers/:id` - Get customer

### Customer Embed (Port 3002)
- `GET /` - Customer form
- `GET /demo` - Integration demo

## Implementation Steps

### Step 1: Frontend Integration
1. Add iframe to your checkout page
2. Implement PostMessage listeners
3. Update your checkout UI with received data

### Step 2: Backend Integration
1. Create endpoint to receive customer data
2. Store customer information in your database
3. Process payment with customer details

### Step 3: Tax Integration
1. Customer embed automatically calculates tax via Numeral
2. Receive tax data via PostMessage
3. Use tax data for final payment processing

## Kafka Events (Optional)

For real-time updates, subscribe to these Kafka topics:

```javascript
// Customer events
{
  topic: 'customer-updates',
  events: [
    'customer.created',
    'customer.updated',
    'customer.payment_method.added'
  ]
}

// Tax events
{
  topic: 'tax-calculations',
  events: [
    'tax.calculated',
    'tax.rate_changed'
  ]
}
```

## Payment Method Reuse with OTP

For returning customers, implement OTP-based payment method reuse:

### 1. Check for Existing Customer
```javascript
// When customer enters email
const existingCustomer = await fetch(`/api/customers/by-email/${email}`);
if (existingCustomer.paymentMethods.length > 0) {
  showPaymentMethodSelection();
}
```

### 2. OTP Verification
```javascript
// Send OTP for payment method reuse
await fetch('/api/customers/send-otp', {
  method: 'POST',
  body: JSON.stringify({ customerId, phone })
});

// Verify OTP
const verified = await fetch('/api/customers/verify-otp', {
  method: 'POST',
  body: JSON.stringify({ customerId, otp })
});
```

## Error Handling

```javascript
function showError(error) {
  // Display user-friendly error message
  const errorDiv = document.getElementById('checkout-errors');
  errorDiv.innerHTML = `<div class="error">${error}</div>`;
  
  // Log for debugging
  console.error('Customer form error:', error);
}
```

## Security Considerations

1. **Origin Validation**: Always check `event.origin` in PostMessage listeners
2. **HTTPS**: Use HTTPS in production
3. **API Keys**: Store API keys securely on backend
4. **Data Validation**: Validate all received data before processing

## Testing

### Local Development
1. Start customer service: `cd backend && npm run dev` (port 3001)
2. Start customer embed: `cd customer-embed && npm run dev` (port 3002)
3. Open demo: `http://localhost:3002/demo`

### Integration Testing
```javascript
// Test customer creation
const testCustomer = {
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  phone: '+1234567890',
  address: '123 Main St',
  city: 'New York',
  state: 'NY',
  zipCode: '10001',
  country: 'US'
};

// Test tax calculation
const testTax = {
  amount: 100.00,
  address: testCustomer.address
};
```

## Production Deployment

### Environment Variables
```bash
# Customer Embed
NEXT_PUBLIC_API_BASE_URL=https://api.yourdomain.com/api/v1

# Customer Service
DATABASE_URL=postgresql://...
KAFKA_BROKERS=kafka1:9092,kafka2:9092
NUMERAL_API_KEY=your_numeral_key
```

### Scaling Considerations
- Customer embed can be deployed to CDN
- Customer service should be load balanced
- Use Redis for session management
- Implement rate limiting for API endpoints

## Support

For integration support:
- Check the demo at `/demo`
- Review API documentation
- Test with provided examples
- Monitor Kafka events for debugging

---

**Next Steps**: After integration, test the complete flow from customer data collection to payment processing.
