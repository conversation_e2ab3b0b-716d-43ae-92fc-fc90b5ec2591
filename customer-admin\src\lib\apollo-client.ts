import { Apollo<PERSON>lient, InMemoryCache, createHttpLink, from } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';

// Customer microservice GraphQL endpoint
const CUSTOMER_GRAPHQL_URL = process.env.NEXT_PUBLIC_CUSTOMER_GRAPHQL_URL;

// Create HTTP link
const httpLink = createHttpLink({
  uri: CUSTOMER_GRAPHQL_URL,
});

// No auth link needed since authentication is disabled
const authLink = setContext((_, { headers }) => {
  // Return headers without authentication
  return {
    headers: {
      ...headers,
      'Content-Type': 'application/json',
    }
  }
});

// Create Apollo Client
export const apolloClient = new ApolloClient({
  link: from([authLink, httpLink]),
  cache: new InMemoryCache(),
  defaultOptions: {
    watchQuery: {
      errorPolicy: 'all',
    },
    query: {
      errorPolicy: 'all',
    },
  },
});
